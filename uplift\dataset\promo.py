from pandas import read_excel, to_datetime
from datetime import datetime


PROMO_START_DATE = datetime(2021, 1, 1)


def a3_load_and_preprocess(a3_path, keep_retailers=None):
    """
    Load and preprocess A3 distribution data from an Excel file.

    Arguments:
        a3_path (str): Path to the Excel file.
        keep_retailers (list, optional): List of retailer names to keep. Defaults to None.

    Returns:
        DataFrame: Preprocessed A3 distribution data.
    """
    a3_df = read_excel(a3_path, engine='openpyxl',sheet_name='a3_distribution_july_2025')
    a3_df = a3_df.rename(
        columns={
            'Date de début': 'promo_start_date',
            'Date de fin': 'promo_end_date',
            'Similar SKU Mapping': 'similar_sku_mapping',
        }
    )
    a3_df['promo_start_date'] = to_datetime(a3_df['promo_start_date'], format='%Y-%m-%d')
    a3_df['promo_end_date'] = to_datetime(a3_df['promo_end_date'], format='%Y-%m-%d')
    
    if keep_retailers:
        a3_df = a3_df[a3_df['Enseigne'].isin(keep_retailers)]

    a3_df = a3_df[a3_df['promo_start_date'] >= PROMO_START_DATE]

    return a3_df
