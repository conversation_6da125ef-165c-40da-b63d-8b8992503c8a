"""
NIQ preprocessing utilities.

This file is a verbatim refactor of the `niq_load_and_preprocess` notebook
cell in `FR_Promo_Uplift_ADS.ipynb`.  Only structural tweaks were made:
* wrapped in a module function
* added a shared PROMO_START_DATE constant
* concise docstring & comments
"""

from datetime import datetime
import numpy as np
import pandas as pd
from pandas import DateOffset

# keep in sync with other preprocessing modules
PROMO_START_DATE = datetime(2021, 1, 1)


def niq_load_and_preprocess(
    niq_path: str,
    keep_retailers: list[str] | None = None,
):
    """
    Load + tidy NIQ RAW data.

    Parameters
    ----------
    niq_path : str
        Path to the Excel file (sheet 'RAW').
    keep_retailers : list[str] | None
        Retailers to retain.  If None, all retailers are kept.

    Returns
    -------
    tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]
        (niq_sku, niq_beer, beer_pole) — exactly as produced in the notebook.
        Two helper globals are also populated for downstream convenience:
        * sku_segment_mapping
        * beer_pole_renamed
    """
    # ------------------------------------------------------------------ #
    # IO + first-pass cleaning
    # ------------------------------------------------------------------ #
    niq = pd.read_excel(niq_path, engine="openpyxl", sheet_name="RAW")

    if keep_retailers:
        niq = niq[niq["Market Description"].isin(keep_retailers)]

    niq["date_week_end"] = pd.to_datetime(niq["P End Date"], errors="coerce")
    niq["date_week_start"] = niq["date_week_end"] - DateOffset(days=6)

    # ------------------------------------------------------------------ #
    # Numeric re-casting with snake_case helpers
    # ------------------------------------------------------------------ #
    rename_map = {
        "Promo Value TY": "Promo_Value",
        "Non Promo Price TY": "Non_Promo_Price",
        "Promo Price TY": "Promo_Price",
        "Non Promo Value TY": "Non_Promo_Value",
        "Promo Volume TY": "Promo_Volume",
        "Non Promo Volume TY": "Non_Promo_Volume",
        "Base Volume TY": "Base_Volume",
        "Base Value (PPC) TY": "Base_Value_PPC",
        "Promo Price (PPC) TY": "Promo_Price_PPC",
        "Non Promo Price (PPC) TY": "Non_Promo_Price_PPC",
    }

    for col_src, col_tgt in rename_map.items():
        niq[col_tgt] = niq[col_src].astype(float)

    niq.drop(columns=list(rename_map.keys()), inplace=True)

    # ------------------------------------------------------------------ #
    # Three granularities: beer (total), beer_pole, sku
    # ------------------------------------------------------------------ #
    niq_beer = niq[niq["POLE INTERNE"].isnull()].copy()

    beer_pole = niq[
        (~niq["POLE INTERNE"].isnull()) & niq["BRAND"].isnull() & niq["Sub Brand"].isnull()
    ].copy()

    niq_sku = niq[
        (~niq["POLE INTERNE"].isnull())
        & (~niq["BRAND"].isnull())
        & (~niq["Sub Brand"].isnull())
    ].copy()

    # ------------------------------------------------------------------ #
    # SKU construction helpers
    # ------------------------------------------------------------------ #
    niq_sku["pack_type"] = niq_sku["STANDARDIZED PACK TYPE"]

    niq_sku["qty_in_pack"] = niq_sku["STANDARDIZED NUMBER IN PACK"].replace(
        {
            # singles
            **{f"{n}x": str(n) for n in range(1, 12)},
            # mid-range
            **{f"{n}x": "(12-15)" for n in (12, 13, 14, 15)},
            # large
            **{f"{n}x": "(20-24)" for n in (20, 21, 22, 23, 24)},
            # special
            "30x": "30",
        }
    )

    niq_sku["sku"] = (
        niq_sku["Sub Brand"]
        + " "
        + niq_sku["pack_type"]
        + " "
        + niq_sku["qty_in_pack"]
        + "X"
        + niq_sku["STANDARDIZED PACK SIZE ML"].astype(int).astype(str)
        + "ML"
    )

    # ------------------------------------------------------------------ #
    # Helpers exposed globally for later merges
    # ------------------------------------------------------------------ #
    global sku_segment_mapping, beer_pole_renamed
    sku_segment_mapping = (
        niq_sku[["sku", "POLE INTERNE"]]
        .drop_duplicates()
        .rename(columns={"POLE INTERNE": "segment"})
    )

    beer_pole_for_segments = niq[
        (~niq["POLE INTERNE"].isnull()) & niq["Sub Brand"].isnull()
    ].copy()
    if not beer_pole_for_segments.empty:
        beer_pole_renamed = (
            beer_pole_for_segments.groupby(
                ["Market Description", "date_week_start", "date_week_end", "POLE INTERNE"],
                as_index=False,
            )
            .agg({"Volume TY": "sum", "Value Sales TY": "sum"})
            .assign(**{"Volume TY": lambda df: df["Volume TY"] / 100})
            .rename(
                columns={
                    "POLE INTERNE": "segment",
                    "Volume TY": "segment_volume_hl",
                    "Value Sales TY": "segment_value",
                }
            )
        )

    # ------------------------------------------------------------------ #
    # Aggregations
    # ------------------------------------------------------------------ #
    def _common_aggs():
        return {
            "Volume TY": np.sum,
            "Value Sales TY": np.sum,
            "Promo_Value": np.sum,
            "Non_Promo_Value": np.sum,
            "Promo_Volume": np.sum,
            "Non_Promo_Volume": np.sum,
            "Base_Volume": np.sum,
            "Base_Value_PPC": np.sum,
            "Non_Promo_Price": np.max,
            "Promo_Price": np.max,
            "Non_Promo_Price_PPC": np.max,
            "Promo_Price_PPC": np.max,
        }

    # --- SKU level
    niq_sku = (
        niq_sku.groupby(
            ["Market Description", "date_week_start", "date_week_end", "sku"],
            as_index=False,
        )
        .agg(
            {
                **_common_aggs(),
                "Wtd Dist TY": np.max,
                "Num Dist TY": np.max,
                "ROS Value TY": np.max,
            }
        )
        .assign(
            volume_hl=lambda df: df["Volume TY"] / 100,
            promo_volume_hl=lambda df: df["Promo_Volume"] / 100,
            non_promo_volume_hl=lambda df: df["Non_Promo_Volume"] / 100,
            base_volume_hl=lambda df: df["Base_Volume"] / 100,
            value_eur=lambda df: df["Value Sales TY"],
            w_dist=lambda df: df["Wtd Dist TY"],
            num_dist=lambda df: df["Num Dist TY"],
            ros_value=lambda df: df["ROS Value TY"],
        )
        .drop(columns=["Volume TY", "Value Sales TY", "Wtd Dist TY", "Num Dist TY", "ROS Value TY"])
    )

    # --- Total beer
    niq_beer = (
        niq_beer.groupby(
            ["Market Description", "date_week_start", "date_week_end", "TOTAL BEER"], as_index=False
        )
        .agg(_common_aggs())
        .rename(columns={"TOTAL BEER": "sku"})
        .assign(
            volume_hl=lambda df: df["Volume TY"] / 100,
            promo_volume_hl=lambda df: df["Promo_Volume"] / 100,
            non_promo_volume_hl=lambda df: df["Non_Promo_Volume"] / 100,
            base_volume_hl=lambda df: df["Base_Volume"] / 100,
            value_eur=lambda df: df["Value Sales TY"],
            base_value_PPC=lambda df: df["Base_Value_PPC"],
            non_promo_price_PPC=lambda df: df["Non_Promo_Price_PPC"],
            promo_price_PPC=lambda df: df["Promo_Price_PPC"],
        )
        .drop(
            columns=[
                "Volume TY",
                "Value Sales TY",
                "Base_Value_PPC",
                "Non_Promo_Price_PPC",
                "Promo_Price_PPC",
                "Non_Promo_Volume",
                "Promo_Volume",
                "Base_Volume",
            ]
        )
    )

    # --- Beer pole
    beer_pole = (
        beer_pole.groupby(
            ["Market Description", "date_week_start", "date_week_end", "POLE INTERNE"],
            as_index=False,
        )
        .agg(_common_aggs())
        .rename(columns={"POLE INTERNE": "sku"})
        .assign(
            volume_hl=lambda df: df["Volume TY"] / 100,
            promo_volume_hl=lambda df: df["Promo_Volume"] / 100,
            non_promo_volume_hl=lambda df: df["Non_Promo_Volume"] / 100,
            base_volume_hl=lambda df: df["Base_Volume"] / 100,
            value_eur=lambda df: df["Value Sales TY"],
            base_value_PPC=lambda df: df["Base_Value_PPC"],
            non_promo_price_PPC=lambda df: df["Non_Promo_Price_PPC"],
            promo_price_PPC=lambda df: df["Promo_Price_PPC"],
        )
        .drop(
            columns=[
                "Volume TY",
                "Value Sales TY",
                "Base_Value_PPC",
                "Non_Promo_Price_PPC",
                "Promo_Price_PPC",
                "Non_Promo_Volume",
                "Promo_Volume",
                "Base_Volume",
            ]
        )
    )

    # ------------------------------------------------------------------ #
    # Optional horizon filter (comment out if not needed)
    # ------------------------------------------------------------------ #
    # niq_sku = niq_sku[niq_sku["date_week_start"] >= PROMO_START_DATE]
    # niq_beer = niq_beer[niq_beer["date_week_start"] >= PROMO_START_DATE]
    # beer_pole = beer_pole[beer_pole["date_week_start"] >= PROMO_START_DATE]

    return niq_sku, niq_beer, beer_pole