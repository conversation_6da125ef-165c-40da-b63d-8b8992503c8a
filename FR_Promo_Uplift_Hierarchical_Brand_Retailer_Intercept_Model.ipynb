{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2b9b42d7-6c32-4539-8f59-03fa7b0ad6f2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Hierarchical Mixed-Effects Model Analysis for ABI Promotion Data\n", "\n", "This notebook analyzes the effect of various KPIs on ABI MS Uplift (relative) using a hierarchical/mixed-effects model to account for retailer and brand variability."]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "656364ef-4ee7-4b95-a854-b0b12a00c673", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Imports and Setup"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f50bd20f-179e-4da5-afdd-e830eb209978", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "from statsmodels.regression.mixed_linear_model import MixedLM\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler\n", "import logging\n", "import shap\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6e4cb389-af47-4698-b131-d4bb47355e9f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def load_and_explore_data(data_path):\n", "    \"\"\"Load data and perform initial exploration\"\"\"\n", "\n", "    # Load data\n", "    df_raw = pd.read_csv(data_path)\n", "\n", "    # Basic info\n", "    print(\"=\"*80)\n", "    print(\"DATA OVERVIEW\")\n", "    print(\"=\"*80)\n", "    print(f\"Dataset shape: {df_raw.shape}\")\n", "    print(f\"Columns: {list(df_raw.columns)}\")\n", "    print(\"\\nFirst few rows:\")\n", "    print(df_raw.head())\n", "\n", "    print(\"\\nData types:\")\n", "    print(df_raw.dtypes)\n", "\n", "    print(\"\\nMissing values:\")\n", "    missing_summary = df_raw.isnull().sum()\n", "    print(missing_summary[missing_summary > 0])\n", "\n", "    return df_raw\n", "\n", "# Load the data\n", "data_path = \"/dbfs/FileStore/RevMan/Phase2/ADS/Output/demelted_data\"\n", "df_raw = load_and_explore_data(data_path)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "be5b08bf-65f5-47e2-8a6b-80d8c6c6da50", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Data Cleaning and Feature Engineering"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "19da4c10-2b1b-4156-8085-40c11a48df65", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def clean_and_engineer_features(df_raw):\n", "    \"\"\"Clean data and engineer features for modeling\"\"\"\n", "\n", "    # Start with a copy\n", "    df = df_raw.copy()\n", "\n", "    # Handle missing values and infinities\n", "    df.replace([np.inf, -np.inf, \"\"], np.nan, inplace=True)\n", "\n", "    # Remove duplicates\n", "    print(f\"Before deduplication: {len(df)} rows\")\n", "    df = df.drop_duplicates()\n", "    print(f\"After deduplication: {len(df)} rows\")\n", "\n", "    # Convert date columns\n", "    df['ABI Start'] = pd.to_datetime(df['ABI Start'])\n", "    df['ABI End'] = pd.to_datetime(df['ABI End'])\n", "\n", "    # Create duration in days\n", "    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days\n", "\n", "    # Extract brand and pack information from ABI SKU\n", "    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]\n", "    df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\\(12-15\\)', regex=True).astype(int)\n", "    df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\\(20-24\\)', regex=True).astype(int)\n", "    df['Pack_Type'] = np.where(df['Pack_12_15'] == 1, '12-15',\n", "                              np.where(df['Pack_20_24'] == 1, '20-24', 'Other'))\n", "\n", "    # Create timing variables\n", "    df['Before'] = df['1 wk before'].fillna(0).astype(int)\n", "    df['After'] = df['1 wk after'].fillna(0).astype(int)\n", "    df['Same_Week'] = df['Same Week'].fillna(0).astype(int)\n", "\n", "    # Clean and prepare key variables\n", "    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')\n", "    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')\n", "    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')\n", "    df['Overlapping_Days'] = pd.to_numeric(df['overlapping days'], errors='coerce').fillna(0)\n", "    df['ABI_PTC_Agg'] = pd.to_numeric(df['ABI Promo PTC Agg'])\n", "\n", "    # Target variable\n", "    df['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')\n", "    df = df.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])\n", "    min_uplift = df['ABI_MS_Uplift_Rel_Raw'].min()\n", "    constant = 0.01 if min_uplift > 0 else abs(min_uplift) + 0.01\n", "    df['ABI_MS_Uplift_Rel_Safe'] = df['ABI_MS_Uplift_Rel_Raw'] + constant\n", "    df['ABI_MS_Uplift_Rel'] = np.log(df['ABI_MS_Uplift_Rel_Safe'])\n", "\n", "    # Convert categorical variables\n", "    df['Retailer'] = df['Retailer'].astype('category')\n", "    df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')\n", "    df['Brand'] = df['Brand'].astype('category')\n", "    df['Pack_Type'] = df['Pack_Type'].astype('category')\n", "    df['KSM'] = df['KSM'].astype('category')\n", "\n", "    # Standardize continuous variables\n", "    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg', 'Overlapping_Days', 'ABI_PTC_Agg']\n", "    scaler = StandardScaler()\n", "    for var in continuous_vars:\n", "        if var in df.columns:\n", "            df[f'{var}_std'] = scaler.fit_transform(df[[var]])\n", "\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"FEATURE ENGINEERING SUMMARY\")\n", "    print(\"=\"*80)\n", "    print(f\"Final dataset shape: {df.shape}\")\n", "\n", "    return df\n", "\n", "# Clean and engineer features\n", "df_clean = clean_and_engineer_features(df_raw)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "18a78cb5-8855-48a7-b5a9-36e971f42e1d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Hierarchical Model: Brand & Retailer Intercept"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c927640a-e83d-4ea0-8de6-df6f345e1df1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def build_brand_retailer_model(df):\n", "    \"\"\"Build hierarchical model with crossed random effects for Brand and Retailer.\"\"\"\n", "\n", "    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',\n", "                   'Same_Week', 'Before', 'After', 'Avg_Temp_std',\n", "                   'ABI_vs_Segment_PTC_Index_Agg_std', 'Overlapping_Days_std', 'ABI_Mechanic', 'Retailer',\n", "                   'Brand', 'Pack_Type', 'KSM', 'ABI_PTC_Agg_std']\n", "    df_model = df[model_vars].dropna()\n", "    print(f\"Data for modeling: {df_model.shape[0]} observations\")\n", "\n", "    base_formula = \"\"\"ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std+\n", "                     Same_Week + Before + After + Avg_Temp_std +\n", "                     C(ABI_Mechanic, Treatment('No NIP')) + KSM:Same_Week + KSM:Before + KSM:After + ABI_PTC_Agg_std\"\"\"\n", "\n", "    try:\n", "        # Use variance components for crossed random effects\n", "        vc = {\"Brand\": \"0 + <PERSON>(<PERSON>)\", \"Retailer\": \"0 + C(Retailer)\"}\n", "\n", "        model = MixedLM.from_formula(base_formula, df_model,\n", "                                           groups=df_model[\"Brand\"],\n", "                                           vc_formula=vc).fit()\n", "\n", "        print(\"\\nBrand + Retailer Crossed Random Effects Model Summary:\")\n", "        print(model.summary())\n", "        return model, df_model\n", "    except Exception as e:\n", "        print(f\"Error fitting model: {e}\")\n", "        return None, None\n", "\n", "active_model, df_model = build_brand_retailer_model(df_clean)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "79bcfd93-1e5a-4066-9817-ef886fd0d51a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Model Evaluation (AIC, BIC, R²)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "035363c6-52dd-4517-9766-5ef22eabfb6a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def calculate_aic_bic(model):\n", "    \"\"\"Calculate AIC and BIC for mixed-effects models\"\"\"\n", "    if hasattr(model, 'aic') and hasattr(model, 'bic') and not (np.isnan(model.aic) or np.isnan(model.bic)):\n", "        return model.aic, model.bic\n", "    \n", "    if hasattr(model, 'llf'):\n", "        llf = model.llf\n", "        n_obs = model.nobs\n", "        n_params = len(model.params)\n", "        if hasattr(model, 'cov_re_unscaled'):\n", "            cov_re_shape = model.cov_re_unscaled.shape\n", "            n_params += (cov_re_shape[0] * (cov_re_shape[0] + 1)) // 2\n", "        if hasattr(model, 'scale'):\n", "            n_params += 1\n", "        \n", "        aic = 2 * n_params - 2 * llf\n", "        bic = np.log(n_obs) * n_params - 2 * llf\n", "        return aic, bic\n", "    return None, None\n", "\n", "def calculate_r_squared(model):\n", "    \"\"\"Calculate Pseudo R-squared for the model.\"\"\"\n", "    y_true = model.model.endog\n", "    y_pred = model.fittedvalues\n", "    ss_res = np.sum((y_true - y_pred) ** 2)\n", "    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)\n", "    pseudo_r2 = 1 - (ss_res / ss_tot)\n", "    return pseudo_r2\n", "\n", "if active_model:\n", "    aic, bic = calculate_aic_bic(active_model)\n", "    r_squared = calculate_r_squared(active_model)\n", "    print(f\"AIC: {aic:.2f}\")\n", "    print(f\"BIC: {bic:.2f}\")\n", "    print(f\"Pseudo R-squared: {r_squared:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6d9704b7-b80a-40b6-87df-24fd4fea44d7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## SHAP Feature Importance Analysis"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "83453413-66d3-4dc4-b626-25e03a540dcd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def extract_ksm_interactions_corrected(df_model, active_model):\n", "    \"\"\"Properly extract KSM[T.1] categorical interactions\"\"\"\n", "    model_coeffs = active_model.params\n", "    ksm_coeffs = {name: coeff for name, coeff in model_coeffs.items() if 'KSM[T.1]:' in name}\n", "    \n", "    feature_data = {}\n", "    feature_names = []\n", "    \n", "    direct_features = ['ABI_Duration_Days_std', 'ABI_Coverage_std', 'Avg_Temp_std', \n", "                       'ABI_PTC_Agg_std', 'Same_Week', 'Before', 'After']\n", "    for feat in direct_features:\n", "        if feat in df_model.columns:\n", "            feature_data[feat] = df_model[feat].fillna(0).values\n", "            feature_names.append(feat)\n", "            \n", "    mechanic_coeffs = [name for name in model_coeffs.index if 'C(ABI_Mechanic' in name]\n", "    for coeff_name in mechanic_coeffs:\n", "        if '[T.' in coeff_name:\n", "            mechanic_type = coeff_name.split('[T.')[1].split(']')[0]\n", "            dummy_values = (df_model['ABI_Mechanic'] == mechanic_type).astype(int)\n", "            feature_data[coeff_name] = dummy_values.values\n", "            feature_names.append(coeff_name)\n", "            \n", "    for coeff_name in ksm_coeffs.keys():\n", "        timing_var = coeff_name.split('KSM[T.1]:')[1]\n", "        if timing_var in df_model.columns and 'KSM' in df_model.columns:\n", "            ksm_is_1 = (df_model['KSM'] == 1).astype(int)\n", "            timing_values = df_model[timing_var].fillna(0)\n", "            interaction_values = ksm_is_1 * timing_values\n", "            feature_data[coeff_name] = interaction_values.values\n", "            feature_names.append(coeff_name)\n", "            \n", "    X_final = np.column_stack([feature_data[name] for name in feature_names])\n", "    y_final = df_model['ABI_MS_Uplift_Rel'].values\n", "    \n", "    valid_rows = ~np.isnan(X_final).any(axis=1) & ~np.isnan(y_final)\n", "    X_final = X_final[valid_rows]\n", "    y_final = y_final[valid_rows]\n", "    \n", "    return X_final, y_final, feature_names, feature_data\n", "\n", "X_corrected, y_corrected, feature_names_corrected, _ = extract_ksm_interactions_corrected(df_model, active_model)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "03b8198c-4f0d-4944-8b2a-56974b377c87", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["class KSMCategoricalPredictor:\n", "    \"\"\"Predictor that properly handles KSM[T.1] categorical interactions\"\"\"\n", "    def __init__(self, model, feature_names):\n", "        self.model = model\n", "        self.feature_names = feature_names\n", "        self.coefficients = dict(model.params)\n", "        \n", "    def predict(self, X):\n", "        if X.ndim == 1:\n", "            X = X.reshape(1, -1)\n", "        \n", "        predictions = np.full(X.shape[0], self.coefficients.get('Intercept', 0))\n", "        \n", "        for i, feature_name in enumerate(self.feature_names):\n", "            if feature_name in self.coefficients:\n", "                coeff = self.coefficients[feature_name]\n", "                predictions += coeff * X[:, i]\n", "                \n", "        return predictions\n", "\n", "predictor_ksm = KSMCategoricalPredictor(active_model, feature_names_corrected)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7021abb1-b676-4d93-a72b-3f27a801037e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["shap.initjs()\n", "\n", "# Create SHAP explainer\n", "background_size = min(100, len(X_corrected))\n", "background_indices = np.random.choice(len(X_corrected), background_size, replace=False)\n", "X_background = X_corrected[background_indices]\n", "\n", "explainer_ksm = shap.KernelExplainer(predictor_ksm.predict, X_background)\n", "\n", "# Calculate SHAP values\n", "n_explain = min(50, len(X_corrected))\n", "explain_indices = np.random.choice(len(X_corrected), n_explain, replace=False)\n", "X_explain = X_corrected[explain_indices]\n", "\n", "print(f\"Calculating SHAP values for {n_explain} samples...\")\n", "shap_values_ksm = explainer_ksm.shap_values(X_explain, nsamples=200)\n", "print(\"SHAP values calculated.\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ce0f16be-d496-4093-8c43-383dba25dfeb", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### SHAP Summary Plot"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8f46e16a-3005-49b5-aa81-b37b96bd4121", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["plt.figure(figsize=(14, 10))\n", "shap.summary_plot(shap_values_ksm, X_explain, feature_names=feature_names_corrected, show=False)\n", "plt.title('SHAP Summary Plot - Brand Retailer Crossed Model')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "900cfb92-57e3-433f-b526-0aa6e31e7283", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### SHAP Waterfall Plots"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0ceb889d-8f9e-43bf-8a01-9b2cd85d9220", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["for i in range(min(3, len(X_explain))):\n", "    plt.figure(figsize=(12, 10))\n", "    \n", "    explanation = shap.Explanation(values=shap_values_ksm[i], \n", "                                 base_values=explainer_ksm.expected_value, \n", "                                 data=X_explain[i],\n", "                                 feature_names=feature_names_corrected)\n", "    \n", "    shap.waterfall_plot(explanation, show=False)\n", "    \n", "    sample_idx = explain_indices[i]\n", "    predicted_value = predictor_ksm.predict(X_explain[i:i+1])[0]\n", "    \n", "    plt.title(f'SHAP Waterfall - Sample {sample_idx}\\nPredicted Uplift: {predicted_value:.3f}')\n", "    plt.tight_layout()\n", "    plt.show()"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": null, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 2}, "notebookName": "FR_Promo_Uplift_Hierarchical_Brand_Retailer_Intercept_Model", "widgets": {}}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}